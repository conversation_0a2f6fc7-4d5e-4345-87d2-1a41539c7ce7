import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Card, Button, Table, Tabs, Form, Input, DatePicker, Select, Space, Empty, Spin, Tag, message, Row, Col, Statistic, Progress, Timeline, Badge, List, Modal, Descriptions } from 'antd';
import { Bar<PERSON>hartOutlined, LineChartOutlined, FileTextOutlined, SettingOutlined, ReloadOutlined, SearchOutlined, FilterOutlined, DownloadOutlined, WarningOutlined, CheckCircleOutlined, ClockCircleOutlined, MonitorOutlined, EyeOutlined, StopOutlined, DeleteOutlined, DatabaseOutlined, MessageOutlined, RobotOutlined, ThunderboltOutlined } from '@ant-design/icons';
import { actionSpaceAPI } from '../../services/api/actionspace';
import { agentAPI } from '../../services/api/agent';
import AgentVariables from '../../components/agent/AgentVariables';
import AgentMonitoring from './AgentMonitoring';
import AutonomousTaskMonitoring from './AutonomousTaskMonitoring';
import ConversationHistoryTab from '../workspace/ConversationHistoryTab';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

const MonitoringCenter = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [statistics, setStatistics] = useState({
    activeSpaces: 0,
    totalRuleSets: 0,
    rulesExecuted: 0,
    ruleExecutionRate: 0,
    abnormalExecutions: 0,
    executionsToday: 0
  });

  // 智能体监控相关状态
  const [agents, setAgents] = useState([]);
  const [agentLoading, setAgentLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [agentActiveTab, setAgentActiveTab] = useState('info');

  // 获取监控数据
  useEffect(() => {
    fetchMonitoringData();
    fetchLogs();
    fetchAgents();

    // 设置定时刷新智能体
    const interval = setInterval(fetchAgents, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchMonitoringData = async () => {
    setLoading(true);
    try {
      // 使用示例场景数据替代API调用

      // 模拟统计数据
      setStatistics({
        activeSpaces: 8, // 示例场景中有8个行动空间
        totalRuleSets: 10, // 示例场景中有10个规则集
        rulesExecuted: 825,
        ruleExecutionRate: 94.3,
        abnormalExecutions: 28,
        executionsToday: 142
      });
    } catch (error) {
      console.error('获取监控数据失败:', error);
      message.error('获取监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchLogs = () => {
    // 使用示例场景数据创建日志数据
    const actionSpaces = [
      '全球供应链运营环境',
      '软件团队协作环境',
      '客户服务中心运营环境',
      '零售店铺经营环境',
      '医疗会诊环境',
      '医疗保险理赔处理环境',
      '制造业生产环境',
      '资产管理与投资决策环境'
    ];

    const ruleSets = [
      '供应链风险管理规则集',
      '软件开发管理规则集',
      '客户服务流程规则集',
      '零售店铺运营规则集',
      '医疗会诊规则集',
      '保险理赔规则集',
      '工艺改进规则集',
      '投资组合管理规则集'
    ];

    const ruleNames = [
      '供应商风险应对',
      '供应商风险阈值处理',
      '技术债务管理',
      '资源分配调整',
      '问题重复率监控',
      '问题自动升级',
      '竞争策略调整',
      '竞争应对自动化',
      '专家意见分歧处理',
      '诊断不确定处理',
      '异常理赔处理',
      '欺诈风险升级',
      '材料变化应对',
      '工艺变更回滚',
      '市场波动应对',
      '现金配置调整'
    ];

    const successMessages = [
      '执行成功: 检测到供应商风险上升，已增加安全库存',
      '执行成功: 识别到技术债务超阈值，已安排重构时间',
      '执行成功: 发现问题重复率上升，已向产品团队提交报告',
      '执行成功: 检测到竞争对手促销增强，已调整价格策略',
      '执行成功: 处理专家意见分歧，安排额外检查',
      '执行成功: 识别异常理赔申请，启动专家评审',
      '执行成功: 检测到原材料变化，已调整工艺参数',
      '执行成功: 识别到市场波动性上升，增加现金配置比例'
    ];

    const errorMessages = [
      '执行失败: 无法获取供应商风险数据，系统连接超时',
      '执行失败: 技术债务计算异常，参数不符合预期',
      '执行失败: 问题分类失败，模型返回低置信度',
      '执行失败: 价格策略调整受限，超出授权范围',
      '执行失败: 诊断系统无响应，无法调用额外专家',
      '执行失败: 理赔金额计算错误，数据格式不一致'
    ];

    const mockLogs = [];

    for (let i = 0; i < 50; i++) {
      const timestamp = new Date(Date.now() - i * 1000 * 60 * Math.floor(Math.random() * 30));
      const types = ['info', 'warning', 'error', 'success'];
      const type = types[Math.floor(Math.random() * types.length)];
      const ruleType = Math.random() > 0.5 ? 'llm' : 'logic';
      const spaceIndex = Math.floor(Math.random() * actionSpaces.length);
      const ruleSetIndex = spaceIndex; // 匹配相应的规则集
      const ruleNameIndex = Math.floor(Math.random() * ruleNames.length);

      // 生成消息
      let message;
      if (type === 'error') {
        message = errorMessages[Math.floor(Math.random() * errorMessages.length)];
      } else if (type === 'success') {
        message = successMessages[Math.floor(Math.random() * successMessages.length)];
      } else if (type === 'warning') {
        message = '警告: ' + successMessages[Math.floor(Math.random() * successMessages.length)].replace('执行成功', '可能需要注意');
      } else {
        message = '信息: 规则触发执行，等待结果';
      }

      mockLogs.push({
        id: `log_${i}`,
        timestamp: timestamp,
        type: type,
        ruleType: ruleType,
        actionSpace: actionSpaces[spaceIndex],
        ruleSet: ruleSets[ruleSetIndex],
        ruleName: ruleNames[ruleNameIndex],
        message: message,
        executionTime: Math.random() * 500 + 50 // 50-550ms
      });
    }

    setLogs(mockLogs);
  };

  // 获取智能体列表
  const fetchAgents = async () => {
    try {
      setAgentLoading(true);
      const response = await agentAPI.getAllActive();
      // 如果是数组直接使用，如果是对象格式则提取data属性
      const agentData = Array.isArray(response) ? response : response.data || [];
      setAgents(agentData);
    } catch (error) {
      message.error('获取智能体列表失败');
    } finally {
      setAgentLoading(false);
    }
  };

  // 停止智能体
  const handleStop = async (id) => {
    try {
      await agentAPI.stop(id);
      message.success('智能体已停止');
      fetchAgents();
    } catch (error) {
      message.error('停止智能体失败');
    }
  };

  // 删除智能体
  const handleDelete = async (id) => {
    try {
      await agentAPI.delete(id);
      message.success('智能体已删除');
      fetchAgents();
    } catch (error) {
      message.error('删除智能体失败');
    }
  };

  // 查看智能体详情
  const showDetail = (agent) => {
    setSelectedAgent(agent);
    setAgentActiveTab('info'); // 重置为基本信息标签
    setDetailModalVisible(true);
  };

  // 处理标签页切换
  const handleAgentTabChange = (key) => {
    setAgentActiveTab(key);
  };

  // 仪表盘Tab渲染
  const renderDashboardTab = () => {
    return (
      <div>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchMonitoringData}
            loading={loading}
          >
            刷新数据
          </Button>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃行动空间"
                value={statistics.activeSpaces}
                valueStyle={{ color: '#1677ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="规则集总数"
                value={statistics.totalRuleSets}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日规则执行次数"
                value={statistics.executionsToday}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="异常执行数"
                value={statistics.abnormalExecutions}
                valueStyle={{ color: '#ff4d4f' }}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={12}>
            <Card title="规则执行情况">
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                <div>
                  <Text strong>总执行次数：</Text>
                  <Text>{statistics.rulesExecuted}</Text>
                </div>
                <div>
                  <Text strong>执行成功率：</Text>
                  <Text>{statistics.ruleExecutionRate}%</Text>
                </div>
              </div>

              <div>
                <Text>自然语言规则执行</Text>
                <Progress percent={65} status="active" />

                <Text>逻辑规则执行</Text>
                <Progress percent={35} status="active" />
              </div>
            </Card>
          </Col>

          <Col span={12}>
            <Card title="最近异常">
              <Timeline>
                {logs.filter(log => log.type === 'error' || log.type === 'warning').slice(0, 4).map(log => (
                  <Timeline.Item
                    key={log.id}
                    color={log.type === 'error' ? 'red' : 'orange'}
                  >
                    <p>
                      <Text strong>{log.actionSpace}</Text> -
                      <Text>{log.ruleName}</Text>
                    </p>
                    <p>{log.message}</p>
                    <p>
                      <Text type="secondary">{log.timestamp.toLocaleString()}</Text>
                    </p>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title="最近执行记录" extra={<Button type="link">查看全部</Button>}>
              <List
                size="small"
                dataSource={logs.slice(0, 5)}
                renderItem={log => (
                  <List.Item>
                    <Space>
                      <Badge
                        status={
                          log.type === 'success' ? 'success' :
                          log.type === 'error' ? 'error' :
                          log.type === 'warning' ? 'warning' : 'processing'
                        }
                      />
                      <Text>{log.timestamp.toLocaleString()}</Text>
                      <Tag color={log.ruleType === 'llm' ? 'green' : 'blue'}>
                        {log.ruleType === 'llm' ? '大模型' : '逻辑'}
                      </Tag>
                      <Text strong>{log.actionSpace}</Text>
                      <Text type="secondary">{log.ruleSet}</Text>
                      <Text>{log.ruleName}</Text>
                      <Text type="secondary">{log.executionTime.toFixed(2)}ms</Text>
                    </Space>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 执行日志Tab渲染
  const renderLogsTab = () => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (timestamp) => timestamp.toLocaleString()
      },
      {
        title: '状态',
        dataIndex: 'type',
        key: 'type',
        render: (type) => {
          let color = '';
          let icon = null;

          switch(type) {
            case 'success':
              color = 'success';
              icon = <CheckCircleOutlined />;
              break;
            case 'error':
              color = 'error';
              icon = <WarningOutlined />;
              break;
            case 'warning':
              color = 'warning';
              icon = <WarningOutlined />;
              break;
            default:
              color = 'default';
              icon = <ClockCircleOutlined />;
          }

          return <Tag color={color} icon={icon}>{type.toUpperCase()}</Tag>;
        }
      },
      {
        title: '行动空间',
        dataIndex: 'actionSpace',
        key: 'actionSpace',
      },
      {
        title: '规则集',
        dataIndex: 'ruleSet',
        key: 'ruleSet',
      },
      {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
      },
      {
        title: '规则类型',
        dataIndex: 'ruleType',
        key: 'ruleType',
        render: (type) => <Tag color={type === 'llm' ? 'green' : 'blue'}>{type === 'llm' ? '大模型' : '逻辑'}</Tag>
      },
      {
        title: '执行消息',
        dataIndex: 'message',
        key: 'message',
        ellipsis: true,
      },
      {
        title: '执行时间',
        dataIndex: 'executionTime',
        key: 'executionTime',
        render: (time) => `${time.toFixed(2)}ms`
      },
    ];

    return (
      <div>
        <Card style={{ marginBottom: 16 }}>
          <Form layout="inline">
            <Form.Item label="时间范围">
              <RangePicker showTime />
            </Form.Item>
            <Form.Item label="行动空间">
              <Select placeholder="选择行动空间" style={{ width: 180 }} allowClear>
                <Option value="all">全部</Option>
                <Option value="生态系统模拟">生态系统模拟</Option>
                <Option value="城市环境">城市环境</Option>
                <Option value="历史场景">历史场景</Option>
              </Select>
            </Form.Item>
            <Form.Item label="规则类型">
              <Select placeholder="选择规则类型" style={{ width: 120 }} allowClear>
                <Option value="all">全部</Option>
                <Option value="llm">大模型</Option>
                <Option value="logic">逻辑</Option>
              </Select>
            </Form.Item>
            <Form.Item label="状态">
              <Select placeholder="选择状态" style={{ width: 120 }} allowClear>
                <Option value="all">全部</Option>
                <Option value="success">成功</Option>
                <Option value="error">错误</Option>
                <Option value="warning">警告</Option>
                <Option value="info">信息</Option>
              </Select>
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon={<SearchOutlined />}>搜索</Button>
            </Form.Item>
            <Form.Item>
              <Button icon={<DownloadOutlined />}>导出日志</Button>
            </Form.Item>
          </Form>
        </Card>

        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </div>
    );
  };

  // 监控设置Tab渲染
  const renderSettingsTab = () => {
    return (
      <Empty
        description="监控设置功能开发中"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  };

  // 智能体监控Tab渲染
  const renderAgentsTab = () => {
    // 直接渲染AgentMonitoring组件
    return <AgentMonitoring />;
  };

  // 自主行动监控Tab渲染
  const renderAutonomousTasksTab = () => {
    // 直接渲染AutonomousTaskMonitoring组件
    return <AutonomousTaskMonitoring />;
  };

  // 任务会话Tab渲染
  const renderTaskConversationsTab = () => {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            查看不同行动任务的会话历史记录，包括智能体对话内容和思考过程。
          </Text>
        </div>
        <Card
          variant="borderless"
          style={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
          }}
        >
          <ConversationHistoryTab />
        </Card>
      </div>
    );
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>
        <div>
          <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>行动监控</Title>
          <Text type="secondary">
            实时监控行动空间和智能体的运行状态，查看执行日志和配置监控告警规则
          </Text>
        </div>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><BarChartOutlined />仪表盘</span>}
          key="dashboard"
        >
          {renderDashboardTab()}
        </TabPane>
        <TabPane
          tab={<span><RobotOutlined />智能体监控</span>}
          key="agents"
        >
          {renderAgentsTab()}
        </TabPane>
        <TabPane
          tab={<span><ThunderboltOutlined />自主行动监控</span>}
          key="autonomous"
        >
          {renderAutonomousTasksTab()}
        </TabPane>
        <TabPane
          tab={<span><MessageOutlined />任务会话</span>}
          key="conversations"
        >
          {renderTaskConversationsTab()}
        </TabPane>
        <TabPane
          tab={<span><FileTextOutlined />执行日志</span>}
          key="logs"
        >
          {renderLogsTab()}
        </TabPane>
        <TabPane
          tab={<span><SettingOutlined />监控设置</span>}
          key="settings"
        >
          {renderSettingsTab()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default MonitoringCenter;