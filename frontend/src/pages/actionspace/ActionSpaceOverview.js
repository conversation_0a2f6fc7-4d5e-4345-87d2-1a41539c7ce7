import React, { useState, useEffect, useRef } from 'react';
import {
  Card, Button, Table, Tabs, Empty,
  Space, Modal, Form, Input, message,
  Typography, Tag, Select, Radio, Dropdown, Menu, Spin, Collapse, InputNumber,
  Row, Col, Avatar, Tooltip
} from 'antd';
import {
  PlusOutlined, TableOutlined, AppstoreOutlined,
  EditOutlined, DeleteOutlined, InfoCircleOutlined,
  FilterOutlined, EllipsisOutlined, LinkOutlined,
  FileTextOutlined, TeamOutlined, ExclamationCircleOutlined,
  TagsOutlined, RobotOutlined
} from '@ant-design/icons';
import { actionSpaceAPI } from '../../services/api/actionspace';
import { modelConfigAPI } from '../../services/api/model';
import { settingsAPI } from '../../services/api/settings';
import api from '../../services/api/axios';
import { useNavigate } from 'react-router-dom';
import TagManagementModal from '../../components/TagManagementModal';
import { replaceTemplateVariables } from '../../utils/templateUtils';
import { getAssistantGenerationModelId } from '../../utils/modelUtils';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

const ActionSpaceOverview = () => {
  const [viewMode, setViewMode] = useState('card'); // 'card' or 'table'
  const [actionSpaces, setActionSpaces] = useState([]);
  const [allActionSpaces, setAllActionSpaces] = useState([]); // 存储所有行动空间
  const [loading, setLoading] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 标签相关状态
  const [industryTags, setIndustryTags] = useState([]);
  const [scenarioTags, setScenarioTags] = useState([]);
  const [tagsLoading, setTagsLoading] = useState(false);

  // 标签筛选状态
  const [selectedTagIds, setSelectedTagIds] = useState([]);
  const [tagsVisible, setTagsVisible] = useState(false);

  // 标签管理Modal状态
  const [tagManagementVisible, setTagManagementVisible] = useState(false);

  // 添加新的状态存储模型配置
  const [modelConfigs, setModelConfigs] = useState([]);

  // 辅助生成相关状态
  const [assistantGenerating, setAssistantGenerating] = useState({
    background: false,
    rules: false
  });
  const [globalSettings, setGlobalSettings] = useState({
    enableAssistantGeneration: true,
    assistantGenerationModel: 'default'
  });

  const navigate = useNavigate();

  // 获取所有模型配置
  const fetchModelConfigs = async () => {
    try {
      const models = await modelConfigAPI.getAll();
      setModelConfigs(models);
      return models;
    } catch (error) {
      console.error('获取模型配置失败:', error);
      return [];
    }
  };

  // 根据模型ID获取模型名称
  const getModelNameById = (modelId) => {
    if (!modelId) return '未指定';

    const modelConfig = modelConfigs.find(m => m.id.toString() === modelId.toString());
    if (modelConfig) {
      return `${modelConfig.name} (${modelConfig.model_id})`;
    }

    // 如果没找到配置，返回ID
    return `模型 ID: ${modelId}`;
  };

  // 获取全局设置
  const fetchGlobalSettings = async () => {
    try {
      const settings = await settingsAPI.getSettings();
      setGlobalSettings({
        enableAssistantGeneration: settings.enableAssistantGeneration !== undefined ? settings.enableAssistantGeneration : true,
        assistantGenerationModel: settings.assistantGenerationModel || 'default'
      });
    } catch (error) {
      console.error('获取全局设置失败:', error);
    }
  };

  // 获取行动空间列表
  useEffect(() => {
    fetchActionSpaces();
    fetchModelConfigs(); // 添加获取模型配置
    fetchGlobalSettings(); // 添加获取全局设置
  }, []);

  // 在前端进行标签筛选
  useEffect(() => {
    if (selectedTagIds.length > 0) {
      // 前端筛选：只显示包含所有选中标签的行动空间（"与"关系）
      const filteredSpaces = allActionSpaces.filter(space => {
        const spaceTags = space.tags || [];
        const spaceTagIds = spaceTags.map(tag => tag.id);
        // 检查所有选中的标签是否都存在于当前空间的标签中
        return selectedTagIds.every(tagId =>
          spaceTagIds.includes(tagId)
        );
      });
      // 筛选后也要保持按创建时间降序排序
      const sortedFilteredSpaces = filteredSpaces.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      setActionSpaces(sortedFilteredSpaces);
    } else {
      // 没有选择标签时显示所有行动空间（已经排序过）
      setActionSpaces(allActionSpaces);
    }
  }, [selectedTagIds, allActionSpaces]);

  const fetchActionSpaces = async () => {
    setLoading(true);
    setTagsLoading(true);
    try {
      // 同时获取标签和行动空间数据
      const [spacesResponse, tagsResponse] = await Promise.all([
        actionSpaceAPI.getAll(),
        actionSpaceAPI.getAllTags()
      ]);

      // 处理行动空间数据
      const processedSpaces = spacesResponse.map(space => {
        return {
          ...space,
          tags: Array.isArray(space.tags) ? space.tags : [] // 确保tags是数组
        };
      });

      // 按创建时间降序排序，最新的在前面
      const sortedSpaces = processedSpaces.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      setAllActionSpaces(sortedSpaces); // 保存所有行动空间
      setActionSpaces(sortedSpaces); // 默认显示所有行动空间

      // 处理标签数据
      const tags = tagsResponse || [];

      // 根据类型分类标签
      const industry = tags.filter(tag => tag.type === 'industry');
      const scenario = tags.filter(tag => tag.type === 'scenario');

      setIndustryTags(industry);
      setScenarioTags(scenario);
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
      // 使用空数组确保UI不会崩溃
      setAllActionSpaces([]);
      setActionSpaces([]);
      setIndustryTags([]);
      setScenarioTags([]);
    } finally {
      setLoading(false);
      setTagsLoading(false);
    }
  };

  const handleCreateSpace = () => {
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  // 辅助生成背景设定
  const handleAssistantGenerateBackground = async () => {
    try {
      if (!globalSettings.enableAssistantGeneration) {
        message.warning('辅助生成功能未启用，请在系统设置中开启');
        return;
      }

      const values = form.getFieldsValue(['name', 'description']);

      if (!values.name || !values.description) {
        message.warning('请先填写行动空间名称和描述，然后再使用辅助生成');
        return;
      }

      setAssistantGenerating(prev => ({ ...prev, background: true }));

      // 获取系统设置的提示词模板
      let promptTemplate;
      try {
        const templates = await settingsAPI.getPromptTemplates();
        promptTemplate = templates.actionSpaceBackground;
      } catch (error) {
        console.error('获取提示词模板失败，使用默认模板:', error);
        // 使用默认模板
        promptTemplate = `请根据以下多智能体行动空间信息生成专业的背景设定：

行动空间名称：{{name}}
行动空间描述：{{description}}

要求：
1. 背景设定应该详细描述行动空间的环境、场景和上下文
2. 包含相关的历史背景、现状分析和发展趋势
3. 明确行动空间的目标和意义
4. 使用生动、具体的语言描述
5. 为参与者提供充分的情境信息

请直接返回背景设定内容，不需要额外的解释。`;
      }

      // 使用模板变量替换功能
      const generatePrompt = replaceTemplateVariables(promptTemplate, {
        name: values.name,
        description: values.description
      });

      const modelToUse = await getAssistantGenerationModelId(modelConfigs, globalSettings.assistantGenerationModel);

      let generatedContent = '';
      const handleStreamResponse = (chunk) => {
        if (chunk && chunk !== 'null' && chunk !== 'undefined' && typeof chunk === 'string') {
          generatedContent += chunk;
          form.setFieldsValue({
            background: generatedContent
          });
        }
      };

      await modelConfigAPI.testModelStream(
        modelToUse,
        generatePrompt,
        handleStreamResponse,
        "你是一个专业的场景设计师，擅长根据空间描述生成详细的背景设定。",
        {
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      const cleanedContent = generatedContent
        .replace(/null/g, '')
        .replace(/undefined/g, '')
        .trim();

      form.setFieldsValue({
        background: cleanedContent
      });

      message.success('背景设定生成完成');
    } catch (error) {
      console.error('辅助生成背景设定失败:', error);
      message.error(`辅助生成失败: ${error.message || '未知错误'}`);
    } finally {
      setAssistantGenerating(prev => ({ ...prev, background: false }));
    }
  };

  // 辅助生成基本规则
  const handleAssistantGenerateRules = async () => {
    try {
      if (!globalSettings.enableAssistantGeneration) {
        message.warning('辅助生成功能未启用，请在系统设置中开启');
        return;
      }

      const values = form.getFieldsValue(['name', 'description']);

      if (!values.name || !values.description) {
        message.warning('请先填写行动空间名称和描述，然后再使用辅助生成');
        return;
      }

      setAssistantGenerating(prev => ({ ...prev, rules: true }));

      // 获取系统设置的提示词模板
      let promptTemplate;
      try {
        const templates = await settingsAPI.getPromptTemplates();
        promptTemplate = templates.actionSpaceRules;
      } catch (error) {
        console.error('获取提示词模板失败，使用默认模板:', error);
        // 使用默认模板
        promptTemplate = `请根据以下行动空间信息生成专业的基本规则：

行动空间名称：{{name}}
行动空间描述：{{description}}

要求：
1. 基本规则应该明确定义行动空间内的行为准则和约束条件
2. 包含参与者的权限和责任范围
3. 规定交互方式和协作机制
4. 明确决策流程和执行标准
5. 包含风险控制和异常处理规则
6. 使用清晰、准确的语言表述
7. 条理清晰，便于理解和执行

请直接返回基本规则内容，不需要额外的解释。`;
      }

      // 使用模板变量替换功能
      const generatePrompt = replaceTemplateVariables(promptTemplate, {
        name: values.name,
        description: values.description
      });

      const modelToUse = await getAssistantGenerationModelId(modelConfigs, globalSettings.assistantGenerationModel);

      let generatedContent = '';
      const handleStreamResponse = (chunk) => {
        if (chunk && chunk !== 'null' && chunk !== 'undefined' && typeof chunk === 'string') {
          generatedContent += chunk;
          form.setFieldsValue({
            rules: generatedContent
          });
        }
      };

      await modelConfigAPI.testModelStream(
        modelToUse,
        generatePrompt,
        handleStreamResponse,
        "你是一个专业的规则制定专家，擅长根据空间描述生成详细的行为规则。",
        {
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      const cleanedContent = generatedContent
        .replace(/null/g, '')
        .replace(/undefined/g, '')
        .trim();

      form.setFieldsValue({
        rules: cleanedContent
      });

      message.success('基本规则生成完成');
    } catch (error) {
      console.error('辅助生成基本规则失败:', error);
      message.error(`辅助生成失败: ${error.message || '未知错误'}`);
    } finally {
      setAssistantGenerating(prev => ({ ...prev, rules: false }));
    }
  };

  const handleModalSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建行动空间数据
      const spaceData = {
        name: values.name,
        description: values.description,
        rules: values.rules || '',
        settings: {
          background: values.background || ''
        },
        tag_ids: values.tag_ids || [] // 添加标签IDs
      };

      // 创建新的行动空间
      await actionSpaceAPI.create(spaceData);
      message.success('行动空间创建成功');
      setIsModalVisible(false);

      // 重新获取行动空间列表，确保获取最新数据
      await fetchActionSpaces();
    } catch (error) {
      console.error('创建行动空间失败:', error);
      message.error('创建行动空间失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSpace = (space) => {
    // 确认对话框
    Modal.confirm({
      title: '确认删除行动空间',
      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      content: (
        <div>
          <p>您确定要删除行动空间 <strong>"{space.name}"</strong> 吗？</p>
          <p><b>警告：</b>此操作将永久删除以下内容，且<b>不可恢复</b>：</p>
          <ul>
            <li>行动空间基本信息</li>
            <li>与规则集的关联关系（规则集本身不会被删除）</li>
            <li>与角色的关联关系（角色本身不会被删除）</li>
            <li>行动空间专属的环境变量配置</li>
            <li>角色在该行动空间中的变量配置</li>
            <li>监督者配置</li>
          </ul>
          <p><b>注意：</b>如果存在关联的行动任务，将无法删除此行动空间。</p>
        </div>
      ),
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await actionSpaceAPI.delete(space.id);

          // 更新两个状态
          setAllActionSpaces(prev => prev.filter(s => s.id !== space.id));
          setActionSpaces(prev => prev.filter(s => s.id !== space.id));

          message.success('行动空间删除成功');
        } catch (error) {
          console.error('删除行动空间失败:', error);

          // 处理特定错误信息
          if (error.response?.data?.error) {
            const errorMsg = error.response.data.error;
            const relatedTasks = error.response.data.related_tasks;

            if (errorMsg.includes('关联的行动任务') && relatedTasks) {
              // 显示关联任务的详细信息
              Modal.info({
                title: '无法删除行动空间',
                icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
                content: (
                  <div>
                    <p>行动空间 <strong>"{space.name}"</strong> 无法删除，因为存在以下关联的行动任务：</p>
                    <div style={{ marginTop: 12, marginBottom: 12, maxHeight: '300px', overflowY: 'auto' }}>
                      {relatedTasks.map(task => (
                        <Card key={task.id} size="small" style={{ marginBottom: 8 }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                            <div style={{ flex: 1 }}>
                              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                                {task.title}
                              </div>
                              <div style={{ fontSize: '12px', color: '#666', marginBottom: 4 }}>
                                ID: {task.id} | 状态: {task.status === 'active' ? '进行中' :

                                                    task.status === 'completed' ? '已完成' : task.status}
                              </div>
                              {task.description && (
                                <div style={{ fontSize: '12px', color: '#888' }}>
                                  {task.description.length > 50 ?
                                    `${task.description.substring(0, 50)}...` :
                                    task.description}
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                    <div style={{ backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px', padding: '12px', marginTop: 16 }}>
                      <p style={{ margin: 0, fontWeight: 'bold', color: '#52c41a' }}>建议操作：</p>
                      <ul style={{ margin: '8px 0 0 0', paddingLeft: '20px' }}>
                        <li>先完成或删除相关的行动任务</li>
                        <li>或者将行动任务迁移到其他行动空间</li>
                        <li>可以在任务管理页面中处理这些任务</li>
                      </ul>
                    </div>
                  </div>
                ),
                okText: '我知道了',
                width: 600
              });
            } else {
              message.error(`删除行动空间失败: ${errorMsg}`);
            }
          } else {
            message.error('删除行动空间失败');
          }
        }
      }
    });
  };

  const handleSpaceSelect = (space) => {
    // 导航到详情页面
    navigate(`/actionspace/detail/${space.id}`);
  };

  // 处理标签管理
  const handleTagManagement = () => {
    setTagManagementVisible(true);
  };

  // 处理标签管理Modal关闭
  const handleTagManagementCancel = () => {
    setTagManagementVisible(false);
  };

  // 处理标签变更后的回调
  const handleTagsChange = () => {
    // 重新获取标签和行动空间数据
    fetchActionSpaces();
  };

  // 智能标签组件 - 检测溢出并显示tooltip
  const SmartTagsContainer = ({ tags }) => {
    const containerRef = useRef(null);
    const [isOverflowing, setIsOverflowing] = useState(false);

    useEffect(() => {
      const checkOverflow = () => {
        if (containerRef.current) {
          const container = containerRef.current;
          const isOverflow = container.scrollHeight > container.clientHeight ||
                           container.scrollWidth > container.clientWidth;
          setIsOverflowing(isOverflow);
        }
      };

      checkOverflow();

      // 监听窗口大小变化
      window.addEventListener('resize', checkOverflow);
      return () => window.removeEventListener('resize', checkOverflow);
    }, [tags]);

    // 创建标签元素
    const tagElements = tags.map(tag => (
      <Tag
        key={`tag-${tag.id || Math.random().toString(36).substr(2, 10)}`}
        color={tag.color || '#1890ff'}
        style={{
          marginRight: 4,
          marginBottom: 4,
          borderRadius: 4,
          fontSize: '12px',
          padding: '2px 8px',
          border: 'none'
        }}
      >
        {tag.name}
      </Tag>
    ));

    // 创建完整标签列表的文本（用于tooltip）
    const allTagsText = tags.map(tag => tag.name).join('、');

    const containerContent = (
      <div
        ref={containerRef}
        className={`action-space-tags-container ${isOverflowing ? 'overflow' : ''}`}
        style={{
          marginTop: 8,
          marginBottom: 4,
          maxHeight: '60px', // 限制最大高度，超出则溢出
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        {tagElements}
      </div>
    );

    // 只有在溢出时才显示tooltip
    if (isOverflowing) {
      return (
        <Tooltip
          title={`所有标签：${allTagsText}`}
          placement="topLeft"
          overlayStyle={{ maxWidth: '300px' }}
        >
          {containerContent}
        </Tooltip>
      );
    }

    return containerContent;
  };

  // 渲染标签（支持溢出时显示hover文本）
  const renderTags = (tags = []) => {
    if (!tags || tags.length === 0) {
      return null;
    }

    return <SmartTagsContainer tags={tags} />;
  };

  // 渲染标签筛选器
  const renderTagFilter = () => {
    if (!tagsVisible) {
      return null;
    }

    return (
      <Card style={{ marginBottom: 16 }}>
        <div>
          <Title level={5} style={{ marginBottom: 12 }}>行业标签</Title>
          <div style={{ marginBottom: 16 }}>
            {industryTags.map(tag => (
              <Tag
                key={`filter-industry-${tag.id}-${Math.random().toString(36).substr(2, 6)}`}
                color={selectedTagIds.includes(tag.id) ? tag.color : undefined}
                style={{
                  marginRight: 8,
                  marginBottom: 8,
                  cursor: 'pointer',
                  borderRadius: 4,
                  fontSize: '12px',
                  padding: '4px 12px',
                  border: selectedTagIds.includes(tag.id) ? 'none' : `1px solid ${tag.color}`,
                  backgroundColor: selectedTagIds.includes(tag.id) ? tag.color : 'transparent',
                  color: selectedTagIds.includes(tag.id) ? '#fff' : tag.color,
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handleTagClick(tag.id)}
              >
                {tag.name}
              </Tag>
            ))}
          </div>

          <Title level={5} style={{ marginBottom: 12 }}>场景标签</Title>
          <div style={{ marginBottom: 16 }}>
            {scenarioTags.map(tag => (
              <Tag
                key={`filter-scenario-${tag.id}-${Math.random().toString(36).substr(2, 6)}`}
                color={selectedTagIds.includes(tag.id) ? tag.color : undefined}
                style={{
                  marginRight: 8,
                  marginBottom: 8,
                  cursor: 'pointer',
                  borderRadius: 4,
                  fontSize: '12px',
                  padding: '4px 12px',
                  border: selectedTagIds.includes(tag.id) ? 'none' : `1px solid ${tag.color}`,
                  backgroundColor: selectedTagIds.includes(tag.id) ? tag.color : 'transparent',
                  color: selectedTagIds.includes(tag.id) ? '#fff' : tag.color,
                  transition: 'all 0.2s ease'
                }}
                onClick={() => handleTagClick(tag.id)}
              >
                {tag.name}
              </Tag>
            ))}
          </div>

          {selectedTagIds.length > 0 && (
            <Button
              type="link"
              onClick={() => setSelectedTagIds([])}
              style={{ padding: 0 }}
            >
              清除筛选
            </Button>
          )}
        </div>
      </Card>
    );
  };

  // 处理标签点击事件
  const handleTagClick = (tagId) => {
    setSelectedTagIds(prevSelected => {
      if (prevSelected.includes(tagId)) {
        return prevSelected.filter(id => id !== tagId);
      } else {
        return [...prevSelected, tagId];
      }
    });
  };

  // 处理卡片点击事件（排除删除按钮）
  const handleCardClick = (space, event) => {
    // 检查点击的是否是删除按钮或其子元素
    const target = event.target;
    const deleteButton = target.closest('[data-action="delete"]');

    // 如果点击的不是删除按钮，则进入详情页面
    if (!deleteButton) {
      handleSpaceSelect(space);
    }
  };

  // 卡片视图渲染
  const renderCardView = () => {
    // 获取渐变背景类
    const getGradientClass = (index) => {
      const gradientClasses = [
        'action-space-card-cover-gradient-1',
        'action-space-card-cover-gradient-2',
        'action-space-card-cover-gradient-3',
        'action-space-card-cover-gradient-4'
      ];
      return gradientClasses[index % gradientClasses.length];
    };

    if (loading) {
      return (
        <div style={{ position: 'relative' }}>
          {/* 加载指示器 - 绝对定位，不影响布局 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '12px'
          }}>
            <Spin size="large" />
            <div style={{ color: '#1677ff', fontSize: '14px' }}>加载行动空间列表</div>
          </div>

          {/* 页面框架 - 完全透明背景 */}
          <div style={{ opacity: 0.3 }}>
            <Row gutter={[16, 16]} style={{ minHeight: '400px' }}>
              {/* 行动空间卡片框架 */}
              {[1, 2, 3, 4].map(i => (
                <Col xs={24} sm={12} md={8} lg={6} key={i}>
                  <Card
                    hoverable
                    className="action-space-card"
                    style={{ cursor: 'pointer', minHeight: '300px' }}
                    cover={
                      <div
                        className={getGradientClass(i - 1)}
                        style={{
                          height: 120,
                          color: '#333',
                          padding: '16px',
                          position: 'relative'
                        }}
                      >
                        <h3 style={{ color: '#333', marginBottom: '4px', fontWeight: 500 }}>加载中...</h3>
                        <p style={{
                          opacity: 0.85,
                          fontSize: '12px',
                          color: '#444'
                        }}>
                          正在加载行动空间信息...
                        </p>
                      </div>
                    }
                    actions={[
                      <Button type="text" icon={<InfoCircleOutlined />} disabled>详情</Button>,
                      <Button type="text" icon={<DeleteOutlined />} danger disabled>删除</Button>
                    ]}
                  >
                    <div style={{ padding: '0 8px', display: 'flex', flexDirection: 'column', flex: 1 }}>
                      <div className="action-space-tags">
                        {/* 标签框架 */}
                      </div>

                      <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between' }}>
                        <Space>
                          <FileTextOutlined />
                          <span>- 规则集</span>
                        </Space>
                        <Space>
                          <TeamOutlined />
                          <span>- 角色</span>
                        </Space>
                      </div>

                      <div style={{ marginTop: 'auto', paddingBottom: '8px' }}>
                        <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                          创建于: 加载中...
                        </small>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}

              {/* 添加新空间卡片框架 */}
              <Col xs={24} sm={12} md={8} lg={6}>
                <Card
                  hoverable
                  className="add-action-space-card"
                  style={{
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <div style={{ textAlign: 'center', padding: '80px 0' }}>
                    <PlusOutlined style={{ fontSize: '32px', color: '#91caff' }} />
                    <p style={{ marginTop: 8 }}>创建行动空间</p>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        </div>
      );
    }

    return (
      <Row gutter={[16, 16]}>
        {actionSpaces.map((space, index) => (
          <Col xs={24} sm={12} md={8} lg={6} key={space.id}>
            <Card
              hoverable
              className="action-space-card"
              onClick={(e) => handleCardClick(space, e)}
              style={{ cursor: 'pointer' }}
              cover={
                <div
                  className={getGradientClass(index)}
                  style={{
                    height: 120,
                    color: '#333',
                    padding: '16px',
                    position: 'relative'
                  }}
                >
                  <h3 style={{ color: '#333', marginBottom: '4px', fontWeight: 500 }}>{space.name}</h3>
                  <p style={{
                    opacity: 0.85,
                    fontSize: '12px',
                    color: '#444',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {space.description}
                  </p>
                </div>
              }
              actions={[
                <Button
                  type="text"
                  icon={<InfoCircleOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSpaceSelect(space);
                  }}
                >
                  详情
                </Button>,
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  danger
                  data-action="delete"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteSpace(space);
                  }}
                >
                  删除
                </Button>
              ]}
            >
              <div style={{ padding: '0 8px', display: 'flex', flexDirection: 'column', flex: 1 }}>
                <div className="action-space-tags">
                  {renderTags(space.tags)}
                </div>

                <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between' }}>
                  <Space>
                    <FileTextOutlined />
                    <span>{(space.rule_sets || []).length} 规则集</span>
                  </Space>
                  <Space>
                    <TeamOutlined />
                    <span>{(space.roles || []).length} 角色</span>
                  </Space>
                </div>

                <div style={{ marginTop: 'auto', paddingBottom: '8px' }}>
                  <small style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                    创建于: {space.created_at ? new Date(space.created_at).toLocaleString() : '未知'}
                  </small>
                </div>
              </div>
            </Card>
          </Col>
        ))}

        {/* 添加新空间卡片 */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            hoverable
            className="add-action-space-card"
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onClick={handleCreateSpace}
          >
            <div style={{ textAlign: 'center', padding: '80px 0' }}>
              <PlusOutlined style={{ fontSize: '32px', color: '#91caff' }} />
              <p style={{ marginTop: 8 }}>创建行动空间</p>
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  // 表格视图渲染
  const renderTableView = () => {
    const columns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true,
      },
      {
        title: '标签',
        dataIndex: 'tags',
        key: 'tags',
        render: (tags) => renderTags(tags),
      },
      {
        title: '规则集',
        dataIndex: 'rule_sets',
        key: 'rule_sets',
        render: (ruleSets, record) => <>{(ruleSets || []).length}</>,
      },
      {
        title: '会话数',
        dataIndex: 'sessions',
        key: 'sessions',
        render: (sessions, record) => <>{(sessions || []).length}</>,
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (date) => date ? new Date(date).toLocaleString() : '-',
      },
      {
        title: '操作',
        key: 'action',
        width: 180,
        render: (_, record) => (
          <Space size="small">
            <Button size="small" type="primary" onClick={() => handleSpaceSelect(record)}>
              详情
            </Button>
            <Button size="small" danger onClick={() => handleDeleteSpace(record)}>
              删除
            </Button>
          </Space>
        )
      },
    ];

    return (
      <Table
        columns={columns}
        dataSource={actionSpaces}
        rowKey={record => record.id ? `table-row-${record.id}` : `table-row-${Math.random().toString(36).substr(2, 10)}`}
        loading={loading}
        onRow={(record) => ({
          onClick: (event) => {
            // 检查点击的是否是操作按钮
            const target = event.target;
            const actionButton = target.closest('.ant-btn');

            // 如果点击的不是操作按钮，则进入详情页面
            if (!actionButton) {
              handleSpaceSelect(record);
            }
          },
          style: { cursor: 'pointer' }
        })}
      />
    );
  };

  return (
    <div className="action-space-container">
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24
      }}>
        <div>
          <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>行动空间概览</Title>
          <Text type="secondary">
            查看和管理所有行动空间，包括空间状态、关联规则集和运行情况
          </Text>
        </div>

        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateSpace}
        >
          创建行动空间
        </Button>
      </div>

      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <span>行动空间管理</span>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => setTagsVisible(!tagsVisible)}
                type={selectedTagIds.length > 0 ? "primary" : "default"}
              >
                按标签筛选 {selectedTagIds.length > 0 ? `(${selectedTagIds.length})` : ''}
              </Button>
              <Button
                icon={<TagsOutlined />}
                onClick={handleTagManagement}
              >
                标签管理
              </Button>
              <Button.Group>
                <Button
                  type={viewMode === 'card' ? 'primary' : 'default'}
                  icon={<AppstoreOutlined />}
                  onClick={() => setViewMode('card')}
                />
                <Button
                  type={viewMode === 'table' ? 'primary' : 'default'}
                  icon={<TableOutlined />}
                  onClick={() => setViewMode('table')}
                />
              </Button.Group>
            </Space>
          </div>
        }
      >
        {renderTagFilter()}

        {viewMode === 'card' ? renderCardView() : renderTableView()}
      </Card>

      {/* 创建行动空间对话框 */}
      <Modal
        title="创建行动空间"
        visible={isModalVisible}
        onCancel={handleModalCancel}
        onOk={handleModalSubmit}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入行动空间名称' }]}
          >
            <Input placeholder="输入行动空间名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入行动空间描述' }]}
          >
            <TextArea rows={3} placeholder="输入行动空间描述" />
          </Form.Item>

          {/* 添加标签选择 */}
          <Form.Item
            name="tag_ids"
            label="标签"
            extra="选择适合此行动空间的标签"
          >
            <Select
              mode="multiple"
              placeholder="选择标签"
              loading={tagsLoading}
              optionFilterProp="label"
              style={{ width: '100%' }}
            >
              <Select.OptGroup label="行业标签">
                {industryTags.map(tag => (
                  <Option key={tag.id} value={tag.id} label={tag.name}>
                    <Tag color={tag.color}>{tag.name}</Tag>
                  </Option>
                ))}
              </Select.OptGroup>
              <Select.OptGroup label="场景标签">
                {scenarioTags.map(tag => (
                  <Option key={tag.id} value={tag.id} label={tag.name}>
                    <Tag color={tag.color}>{tag.name}</Tag>
                  </Option>
                ))}
              </Select.OptGroup>
            </Select>
          </Form.Item>

          <Form.Item
            name="background"
            label={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                <span>背景设定</span>
                <Button
                  type="link"
                  icon={<RobotOutlined />}
                  onClick={handleAssistantGenerateBackground}
                  loading={assistantGenerating.background}
                  disabled={!globalSettings.enableAssistantGeneration}
                  size="small"
                  style={{
                    color: '#1677ff',
                    fontSize: '12px',
                    padding: '0 4px',
                    height: 'auto'
                  }}
                >
                  辅助生成
                </Button>
              </div>
            }
            extra={
              !globalSettings.enableAssistantGeneration ?
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  辅助生成功能未启用，请在系统设置中开启
                </Text> :
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  点击"辅助生成"可根据行动空间名称和描述自动生成背景设定
                </Text>
            }
          >
            <TextArea
              rows={5}
              placeholder="输入行动空间背景设定"
              style={{
                backgroundColor: assistantGenerating.background ? '#f6ffed' : undefined,
                borderColor: assistantGenerating.background ? '#b7eb8f' : undefined
              }}
            />
          </Form.Item>

          <Form.Item
            name="rules"
            label={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                <span>基本规则</span>
                <Button
                  type="link"
                  icon={<RobotOutlined />}
                  onClick={handleAssistantGenerateRules}
                  loading={assistantGenerating.rules}
                  disabled={!globalSettings.enableAssistantGeneration}
                  size="small"
                  style={{
                    color: '#1677ff',
                    fontSize: '12px',
                    padding: '0 4px',
                    height: 'auto'
                  }}
                >
                  辅助生成
                </Button>
              </div>
            }
            extra={
              !globalSettings.enableAssistantGeneration ?
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  辅助生成功能未启用，请在系统设置中开启
                </Text> :
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  点击"辅助生成"可根据行动空间名称和描述自动生成基本规则
                </Text>
            }
          >
            <TextArea
              rows={5}
              placeholder="输入基本规则"
              style={{
                backgroundColor: assistantGenerating.rules ? '#f6ffed' : undefined,
                borderColor: assistantGenerating.rules ? '#b7eb8f' : undefined
              }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 标签管理Modal */}
      <TagManagementModal
        visible={tagManagementVisible}
        onCancel={handleTagManagementCancel}
        onTagsChange={handleTagsChange}
      />
    </div>
  );
};

export default ActionSpaceOverview;