/* 消息和对话样式 */

/* 消息容器 */
.conversation {
  max-width: 800px;
  margin: 0 auto;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  gap: 12px;
}

.message-content {
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
}

.message.system .message-content {
  background: #f0f2f5;
  font-style: italic;
  text-align: center;
}

.message.thinking .message-content {
  opacity: 0.7;
}

/* 消息样式 */
.message-bubble {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.message-bubble.human {
  background-color: #e6f7ff;
}

.message-bubble.agent {
  background-color: #f5f5f5;
}

/* 打字动画效果 */
.cursor-blink {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #1677ff;
  animation: blink 1s step-end infinite;
  vertical-align: middle;
  margin-left: 2px;
}

@keyframes blink {
  from, to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* 消息滚动容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 24px;
  max-height: 500px;
  padding-right: 8px;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* Conversation Detail Styles */
.conversation-detail {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.conversation-info {
  margin-bottom: 24px;
}

.conversation-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 修复消息框样式 */
.message-item {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* 自动讨论面板脉冲背景 */
.discussion-pulse-bg {
  transform: scale(1);
  opacity: 0.8;
  animation: pulse 2s infinite;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自动讨论横幅 */
.discussion-banner {
  animation: fadeIn 0.3s ease-out;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.25);
  min-height: 70px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.discussion-banner:hover {
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.30);
  transform: translateY(-2px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 智能体头像波纹动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color, 24, 144, 255), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--pulse-color, 24, 144, 255), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color, 24, 144, 255), 0);
  }
}

/* 自主行动卡片动画样式 */
.autonomous-task-card {
  transition: all 0.3s ease;
  position: relative;
}

.autonomous-task-card.updating {
  opacity: 0.8;
}

.autonomous-task-card.updating::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  z-index: 1;
  animation: fadeIn 0.2s ease-out;
}

.autonomous-task-card.updating::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #1677ff;
  border-top-color: transparent;
  border-radius: 50%;
  z-index: 2;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 自主任务项动画 */
.autonomous-task-item {
  animation: fadeIn 0.4s ease-out;
  transition: all 0.3s ease;
}

.autonomous-task-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .conversation-detail {
    padding: 16px;
  }

  .message-item.human {
    margin-left: 24px;
  }

  .message-item.agent {
    margin-right: 24px;
  }

  .message-input {
    flex-direction: column;
  }

  .message-input .ant-btn {
    width: 100%;
  }
}
