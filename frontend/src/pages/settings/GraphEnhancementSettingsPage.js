import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  Radio,
  Input,
  InputNumber,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  message,
  Spin,
  Modal,
  Divider,
  Alert
} from 'antd';
import {
  ShareAltOutlined,
  SettingOutlined,
  <PERSON>boltOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined,
  TestOutlined,
  ClearOutlined,
  SaveOutlined,
  SearchOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import graphEnhancementAPI from '../../services/api/graphEnhancement';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const GraphEnhancementSettingsPage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState(null);
  const [status, setStatus] = useState(null);
  const [localEnabled, setLocalEnabled] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState('lightrag');
  const [testLoading, setTestLoading] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [queryModalVisible, setQueryModalVisible] = useState(false);

  // 加载配置
  const loadConfig = async () => {
    try {
      setLoading(true);
      const result = await graphEnhancementAPI.getConfig();
      
      if (result.success) {
        const configData = result.data || {};
        setConfig(configData);
        setLocalEnabled(configData.enabled || false);
        setSelectedFramework(configData.framework || 'lightrag');
        form.setFieldsValue(configData);
      } else {
        message.error(result.message || '获取配置失败');
      }
    } catch (error) {
      message.error('获取配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 加载状态
  const loadStatus = async () => {
    try {
      const result = await graphEnhancementAPI.getStatus();
      if (result.success) {
        setStatus(result.data);
      }
    } catch (error) {
      console.error('获取状态失败:', error);
    }
  };

  useEffect(() => {
    loadConfig();
    loadStatus();
  }, []);

  // 保存配置
  const handleSaveConfig = async (values) => {
    try {
      setLoading(true);
      const configData = {
        ...values,
        enabled: localEnabled
      };

      const result = await graphEnhancementAPI.saveConfig(configData);
      
      if (result.success) {
        message.success('配置保存成功');
        setConfig(configData);
        loadStatus();
      } else {
        message.error(result.message || '配置保存失败');
      }
    } catch (error) {
      message.error('配置保存失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      setTestLoading(true);
      const values = form.getFieldsValue();

      const result = await graphEnhancementAPI.testConnection({
        framework: values.framework,
        framework_config: values.framework_config || {}
      });

      if (result.success) {
        message.success('连接测试成功');
      } else {
        message.error(result.message || '连接测试失败');
      }
    } catch (error) {
      message.error('连接测试失败: ' + error.message);
    } finally {
      setTestLoading(false);
    }
  };

  // 测试查询
  const handleTestQuery = async (queryData) => {
    try {
      setTestLoading(true);
      const result = await graphEnhancementAPI.testQuery(queryData);

      if (result.success) {
        setTestResult(result.data);
        message.success('查询测试成功');
      } else {
        message.error(result.message || '查询测试失败');
        setTestResult(null);
      }
    } catch (error) {
      message.error('查询测试失败: ' + error.message);
      setTestResult(null);
    } finally {
      setTestLoading(false);
    }
  };

  // 重建索引
  const handleRebuildIndex = async () => {
    Modal.confirm({
      title: '确认重建索引',
      content: '此操作将重新构建图谱索引，可能需要较长时间。确定要继续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const result = await graphEnhancementAPI.rebuildIndex();

          if (result.success) {
            message.success('索引重建已开始，请稍后查看状态');
            loadStatus();
          } else {
            message.error(result.message || '索引重建失败');
          }
        } catch (error) {
          message.error('索引重建失败: ' + error.message);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 清空数据
  const handleClearData = async () => {
    Modal.confirm({
      title: '确认清空数据',
      content: '此操作将清空所有图谱数据，无法恢复。确定要继续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const result = await graphEnhancementAPI.clearGraph();

          if (result.success) {
            message.success('数据清空成功');
            loadStatus();
          } else {
            message.error(result.message || '数据清空失败');
          }
        } catch (error) {
          message.error('数据清空失败: ' + error.message);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const renderFrameworkDescription = (framework) => {
    const descriptions = {
      lightrag: '简单快速，支持多种查询模式，适合快速部署',
      graphiti: '时序感知，企业级功能，适合复杂场景',
      graphrag: '微软方案，适合文档分析'
    };
    return descriptions[framework] || '';
  };

  const renderStatusTag = (status) => {
    const statusConfig = {
      connected: { color: 'green', text: '已连接' },
      disconnected: { color: 'red', text: '未连接' },
      error: { color: 'red', text: '错误' },
      initializing: { color: 'blue', text: '初始化中' }
    };
    
    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading && !config) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="graph-enhancement-settings-container">
      <div style={{ marginBottom: '24px' }}>
        <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>
          <ShareAltOutlined style={{ marginRight: '8px' }} />
          图谱增强设置
        </Title>
        <Text type="secondary">
          配置图谱增强功能，提升知识库检索和会话的准确性与上下文理解能力
        </Text>
      </div>

      {/* 顶部开关区域 */}
      <Card
        title="启用状态"
        style={{ marginBottom: 24 }}
      >
        <Row align="middle" gutter={16}>
          <Col>
            <Switch
              checked={localEnabled}
              checkedChildren="启用"
              unCheckedChildren="禁用"
              onChange={(checked) => {
                setLocalEnabled(checked);
              }}
            />
          </Col>
          <Col flex={1}>
            <Text type="secondary">
              图谱增强可以通过知识图谱技术提升检索准确性和上下文理解能力，作用于知识库与会话两套系统
            </Text>
          </Col>
        </Row>
      </Card>

      {localEnabled && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveConfig}
          initialValues={config}
        >
          {/* 框架选择区域 */}
          <Card
            title={
              <Space>
                <SettingOutlined />
                RAG框架选择
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            <Form.Item name="framework" label="选择框架">
              <Radio.Group onChange={(e) => setSelectedFramework(e.target.value)}>
                <Space direction="vertical">
                  <Radio value="lightrag">
                    <Space>
                      <ThunderboltOutlined />
                      <strong>LightRAG</strong>
                      <Text type="secondary">
                        {renderFrameworkDescription('lightrag')}
                      </Text>
                    </Space>
                  </Radio>
                  <Radio value="graphiti">
                    <Space>
                      <ShareAltOutlined />
                      <strong>Graphiti</strong>
                      <Text type="secondary">
                        {renderFrameworkDescription('graphiti')}
                      </Text>
                    </Space>
                  </Radio>
                  <Radio value="graphrag">
                    <Space>
                      <BarChartOutlined />
                      <strong>GraphRAG</strong>
                      <Text type="secondary">
                        {renderFrameworkDescription('graphrag')}
                      </Text>
                    </Space>
                  </Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Card>

          {/* 框架配置区域 */}
          {selectedFramework === 'lightrag' && (
            <Card
              title="LightRAG 配置"
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'embedding_model']} label="嵌入模型">
                    <Input placeholder="text-embedding-ada-002" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'llm_model']} label="LLM模型">
                    <Input placeholder="gpt-4" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'chunk_size']} label="文档块大小">
                    <InputNumber min={100} max={2000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'chunk_overlap']} label="文档块重叠">
                    <InputNumber min={0} max={500} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {selectedFramework === 'graphiti' && (
            <Card
              title="Graphiti 配置"
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'neo4j_uri']} label="Neo4j URI">
                    <Input placeholder="bolt://localhost:7687" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'neo4j_user']} label="Neo4j 用户名">
                    <Input placeholder="neo4j" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'neo4j_password']} label="Neo4j 密码">
                    <Input.Password placeholder="password" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'embedding_model']} label="嵌入模型">
                    <Input placeholder="text-embedding-ada-002" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {selectedFramework === 'graphrag' && (
            <Card
              title="GraphRAG 配置"
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'api_key']} label="API Key">
                    <Input.Password placeholder="your-api-key" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'api_base']} label="API Base URL">
                    <Input placeholder="https://api.openai.com/v1" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'model']} label="模型">
                    <Input placeholder="gpt-4" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'max_tokens']} label="最大Token数">
                    <InputNumber min={100} max={8000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {/* 操作按钮区域 */}
          <Card
            title="操作"
            style={{ marginBottom: 24 }}
          >
            <Space wrap>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
              >
                保存配置
              </Button>
              <Button
                icon={<TestOutlined />}
                onClick={handleTestConnection}
                loading={testLoading}
              >
                测试连接
              </Button>
              <Button
                icon={<SearchOutlined />}
                onClick={() => setQueryModalVisible(true)}
              >
                测试查询
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRebuildIndex}
                loading={loading}
              >
                重建索引
              </Button>
              <Button
                danger
                icon={<ClearOutlined />}
                onClick={handleClearData}
                loading={loading}
              >
                清空数据
              </Button>
            </Space>
          </Card>
        </Form>
      )}

      {/* 状态监控 */}
      {localEnabled && status && (
        <Card
          title="图谱状态"
          style={{ marginTop: 24 }}
          extra={
            <Button
              size="small"
              onClick={loadStatus}
              icon={<ReloadOutlined />}
            >
              刷新
            </Button>
          }
        >
          <Row gutter={16}>
            <Col span={6}>
              <div>
                <Text strong>连接状态:</Text>{' '}
                {renderStatusTag(status.status)}
              </div>
            </Col>
            <Col span={6}>
              <div>
                <Text strong>框架:</Text> {status.framework}
              </div>
            </Col>
            <Col span={6}>
              <div>
                <Text strong>实体数量:</Text> {status.statistics?.entity_count || 0}
              </div>
            </Col>
            <Col span={6}>
              <div>
                <Text strong>关系数量:</Text> {status.statistics?.relation_count || 0}
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 测试查询Modal */}
      <TestQueryModal
        visible={queryModalVisible}
        onCancel={() => setQueryModalVisible(false)}
        onQuery={handleTestQuery}
        loading={testLoading}
        result={testResult}
        config={config}
      />
    </div>
  );
};

// 测试查询Modal组件
const TestQueryModal = ({ visible, onCancel, onQuery, loading, result, config }) => {
  const [queryForm] = Form.useForm();

  const handleQuery = () => {
    queryForm.validateFields().then(values => {
      onQuery(values);
    });
  };

  return (
    <Modal
      title="图谱查询测试"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
        <Button
          key="query"
          type="primary"
          onClick={handleQuery}
          loading={loading}
        >
          执行查询
        </Button>
      ]}
    >
      <Form
        form={queryForm}
        layout="vertical"
        initialValues={{
          mode: config?.default_query_mode || 'hybrid',
          top_k: config?.top_k || 60,
          chunk_top_k: config?.chunk_top_k || 10,
          response_type: 'Multiple Paragraphs'
        }}
      >
        <Form.Item
          name="query"
          label="查询内容"
          rules={[{ required: true, message: '请输入查询内容' }]}
        >
          <TextArea
            rows={3}
            placeholder="请输入要查询的内容..."
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="mode" label="查询模式">
              <Radio.Group size="small">
                <Radio.Button value="hybrid">Hybrid</Radio.Button>
                <Radio.Button value="local">Local</Radio.Button>
                <Radio.Button value="global">Global</Radio.Button>
                <Radio.Button value="mix">Mix</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="top_k" label="Top-K">
              <InputNumber min={1} max={200} size="small" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="response_type" label="响应类型">
              <Radio.Group size="small">
                <Radio.Button value="Multiple Paragraphs">多段落</Radio.Button>
                <Radio.Button value="Single Paragraph">单段落</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      {result && (
        <div style={{ marginTop: 16 }}>
          <Divider>查询结果</Divider>
          <Alert
            message={
              <Space>
                <Text strong>响应时间:</Text> {result.response_time?.toFixed(2)}s
                <Text strong>查询模式:</Text> {result.query_params?.mode}
                <Text strong>框架:</Text> {result.framework}
              </Space>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
          <Card size="small">
            <Paragraph>
              {result.result}
            </Paragraph>
          </Card>
        </div>
      )}
    </Modal>
  );
};

export default GraphEnhancementSettingsPage;
