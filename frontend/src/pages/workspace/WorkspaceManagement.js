import { useState } from 'react';
import { Tabs, Card, Typography } from 'antd';
import {
  ApartmentOutlined
} from '@ant-design/icons';
import WorkspaceTemplateModal from './WorkspaceTemplateModal';
import DeleteWorkspaceModal from './DeleteWorkspaceModal';

const { Title, Text } = Typography;

/**
 * 项目空间管理主组件
 * 整合所有项目空间类型的标签页，包括分区项目空间功能
 */
const WorkspaceManagement = () => {
  const [activeTab, setActiveTab] = useState('memory-graph');
  const [selectedWorkspace, setSelectedWorkspace] = useState(null);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [isTemplateModalVisible, setIsTemplateModalVisible] = useState(false);

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    setSelectedWorkspace(null); // 切换标签页时清空选中的项目文件
  };

  // 处理选择项目文件
  const handleSelectWorkspace = (workspace) => {
    setSelectedWorkspace(workspace);
  };

  // 显示删除确认对话框
  const showDeleteModal = () => {
    if (!selectedWorkspace) {
      return;
    }
    setIsDeleteModalVisible(true);
  };

  // 显示创建模板对话框
  const showTemplateModal = () => {
    if (!selectedWorkspace) {
      return;
    }
    setIsTemplateModalVisible(true);
  };

  // 确认删除项目文件
  const confirmDelete = () => {
    if (selectedWorkspace) {
      // 这里应该调用API删除项目文件
      console.log('删除项目文件:', selectedWorkspace);
      setSelectedWorkspace(null);
      setIsDeleteModalVisible(false);
    }
  };

  // 创建模板
  const handleCreateTemplate = (templateData) => {
    // 这里应该调用API创建模板
    console.log('创建模板:', templateData);
    setIsTemplateModalVisible(false);
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20
        }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>分区记忆</Title>
            <Text type="secondary">
              管理不同类型的记忆，包括记忆图谱等功能
            </Text>
          </div>
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={[
          {
            key: 'memory-graph',
            label: (
              <span>
                <ApartmentOutlined style={{ color: '#722ed1' }} />
                记忆图谱
              </span>
            ),
            children: (
              <Card
                variant="borderless"
                style={{
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                }}
              >
                <div style={{ padding: '40px', textAlign: 'center' }}>
                  <ApartmentOutlined style={{ fontSize: '48px', color: '#722ed1', marginBottom: '16px' }} />
                  <Typography.Title level={4}>记忆图谱</Typography.Title>
                  <Typography.Text type="secondary">
                    基于Graphiti的记忆图谱功能正在开发中，将提供智能体记忆的图形化展示和管理功能。
                  </Typography.Text>
                </div>
              </Card>
            )
          }
        ]}
      />

      {/* 删除确认对话框 */}
      <DeleteWorkspaceModal
        visible={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        onConfirm={confirmDelete}
      />

      {/* 创建模板对话框 */}
      <WorkspaceTemplateModal
        visible={isTemplateModalVisible}
        onCancel={() => setIsTemplateModalVisible(false)}
        onSubmit={handleCreateTemplate}
        workspace={selectedWorkspace}
      />
    </div>
  );
};

export default WorkspaceManagement;
