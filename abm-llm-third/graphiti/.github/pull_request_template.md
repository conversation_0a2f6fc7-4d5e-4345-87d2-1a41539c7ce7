## Summary
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Performance improvement
- [ ] Documentation/Tests

## Objective
**For new features and performance improvements:** Clearly describe the objective and rationale for this change.

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] All existing tests pass

## Breaking Changes
- [ ] This PR contains breaking changes

If this is a breaking change, describe:
- What functionality is affected
- Migration path for existing users

## Checklist
- [ ] Code follows project style guidelines (`make lint` passes)
- [ ] Self-review completed
- [ ] Documentation updated where necessary
- [ ] No secrets or sensitive information committed

## Related Issues
Closes #[issue number]