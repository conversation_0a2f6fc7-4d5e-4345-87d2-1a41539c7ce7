[project]
name = "graph-service"
version = "0.1.0"
description = "Zep Graph service implementing Graphiti package"
authors = [
    { "name" = "<PERSON>", "email" = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.115.0",
    "graphiti-core",
    "pydantic-settings>=2.4.0",
    "uvicorn>=0.30.6",
    "httpx>=0.28.1",
]

[project.optional-dependencies]
dev = [
    "pydantic>=2.8.2",
    "pyright>=1.1.380",
    "pytest>=8.3.2",
    "python-dotenv>=1.0.1",
    "pytest-asyncio>=0.24.0",
    "pytest-xdist>=3.6.1",
    "ruff>=0.6.2",
    "fastapi-cli>=0.0.5",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["graph_service"]

[tool.pytest.ini_options]
pythonpath = ["."]

[tool.ruff]
line-length = 100

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "single"
indent-style = "space"
docstring-code-format = true

[tool.pyright]
include = ["."]
pythonVersion = "3.10"
typeCheckingMode = "standard"
